import java.time.LocalDate;

public class Registration {
    private int id;
    private Member member; // Many-to-one relationship
    private Subscription subscription; // Many-to-one relationship
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private String etat;

    public Registration(int id, Member member, Subscription subscription, LocalDate dateDebut, LocalDate dateFin, String etat) {
        this.id = id;
        this.member = member;
        this.subscription = subscription;
        this.dateDebut = dateDebut;
        this.dateFin = dateFin;
        this.etat = etat;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Subscription getSubscription() {
        return subscription;
    }

    public void setSubscription(Subscription subscription) {
        this.subscription = subscription;
    }

    public LocalDate getDateDebut() {
        return dateDebut;
    }

    public void setDateDebut(LocalDate dateDebut) {
        this.dateDebut = dateDebut;
    }

    public LocalDate getDateFin() {
        return dateFin;
    }

    public void setDateFin(LocalDate dateFin) {
        this.dateFin = dateFin;
    }

    public String getEtat() {
        return etat;
    }

    public void setEtat(String etat) {
        this.etat = etat;
    }
}