# Gym Management System

A JavaFX application for managing a gym, including members, subscriptions, and registrations.

## Features

- **User Authentication**: Login and registration system for secure access.
- **Member Management**: Create, read, update, and delete member information.
- **Subscription Management**: Manage different types of subscriptions with their respective durations and prices.
- **Registration Process**: Handle the registration of members to subscriptions with start and end dates.
- **Database Connectivity**: Connects to a MySQL database for persistent data storage.

## Project Structure

```
gym-management-system
├── LoginScreen.java            # Login screen
├── RegistrationScreen.java     # User registration screen
├── MainDashboard.java          # Main dashboard
├── MembersManagement.java      # Member management screen
├── SubscriptionsManagement.java # Subscription management screen
├── RegistrationsManagement.java # Registration management screen
├── Member.java                 # Member model
├── Subscription.java           # Subscription model
├── Registration.java           # Registration model
├── setup-database.sql          # SQL script for database setup
├── setup-database.bat          # Script to set up the database
├── run-app.bat                 # Script to compile and run the application
└── README.md                   # Project documentation
```

## Prerequisites

- Java JDK 11 or higher
- XAMPP (for MySQL database)

## Setup

1. **Start XAMPP**:
   - Start the Apache and MySQL services in XAMPP Control Panel.

2. **Set up the database**:
   - Run the `setup-database.bat` script to create the database and tables.

3. **Run the application**:
   - Run the `run-app.bat` script to compile and run the application.

## Usage

1. **Login**:
   - Use the default username `admin` and password `admin` to log in.
   - Or register a new user account.

2. **Dashboard**:
   - From the dashboard, you can access the different management screens.

3. **Member Management**:
   - Add, edit, delete, and view gym members.

4. **Subscription Management**:
   - Manage different subscription types, durations, and prices.

5. **Registration Management**:
   - Register members for subscriptions, set start and end dates, and manage registration status.

## Database Structure

- **users**: Stores user accounts for application login.
- **membres**: Stores member information.
- **abonnements**: Stores subscription types and details.
- **inscriptions**: Stores registrations linking members to subscriptions.

## Default Login

- Username: admin
- Password: admin