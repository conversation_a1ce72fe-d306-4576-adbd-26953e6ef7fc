package com.gymmanagement.controllers;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.gymmanagement.models.Subscription;
import com.gymmanagement.models.Registration;
import com.gymmanagement.utils.DatabaseConnection;

public class SubscriptionController {

    // Method to create a new subscription
    public void createSubscription(String type, int dureeJours, double prix) {
        String query = "INSERT INTO abonnements (type, duree_jours, prix) VALUES (?, ?, ?)";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, type);
            statement.setInt(2, dureeJours);
            statement.setDouble(3, prix);
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to read a subscription by ID
    public Subscription readSubscription(int id) {
        String query = "SELECT * FROM abonnements WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, id);
            ResultSet resultSet = statement.executeQuery();
            if (resultSet.next()) {
                return new Subscription(
                    resultSet.getInt("id"),
                    resultSet.getString("type"),
                    resultSet.getInt("duree_jours"),
                    resultSet.getDouble("prix")
                );
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    // Method to update an existing subscription
    public void updateSubscription(int id, String type, int dureeJours, double prix) {
        String query = "UPDATE abonnements SET type = ?, duree_jours = ?, prix = ? WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, type);
            statement.setInt(2, dureeJours);
            statement.setDouble(3, prix);
            statement.setInt(4, id);
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to delete a subscription by ID
    public void deleteSubscription(int id) {
        String query = "DELETE FROM abonnements WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, id);
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to list all subscriptions
    public List<Subscription> listAllSubscriptions() {
        List<Subscription> subscriptions = new ArrayList<>();
        String query = "SELECT * FROM abonnements";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                subscriptions.add(new Subscription(
                    resultSet.getInt("id"),
                    resultSet.getString("type"),
                    resultSet.getInt("duree_jours"),
                    resultSet.getDouble("prix")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return subscriptions;
    }

    // Populate registrations for a subscription
    public List<Registration> getRegistrationsForSubscription(int subscriptionId) {
        List<Registration> registrations = new ArrayList<>();
        String query = "SELECT * FROM inscriptions WHERE abonnement_id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, subscriptionId);
            ResultSet resultSet = statement.executeQuery();
            while (resultSet.next()) {
                registrations.add(new Registration(
                    resultSet.getInt("id"),
                    new com.gymmanagement.controllers.MemberController().readMember(resultSet.getInt("membre_id")),
                    null, // Subscription can be set later if needed
                    resultSet.getDate("date_debut").toLocalDate(),
                    resultSet.getDate("date_fin").toLocalDate(),
                    resultSet.getString("etat")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return registrations;
    }
}