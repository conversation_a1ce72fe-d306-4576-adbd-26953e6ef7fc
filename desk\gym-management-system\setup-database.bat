@echo off
echo Setting up the database...

:: Check if XAMPP is installed
if not exist "C:\xampp\mysql\bin\mysql.exe" (
    echo XAMPP MySQL not found. Please make sure XAMPP is installed.
    pause
    exit /b 1
)

:: Drop and recreate the database
echo Dropping and recreating database...
C:\xampp\mysql\bin\mysql -u root -e "DROP DATABASE IF EXISTS gymdb; CREATE DATABASE gymdb;"

:: Import the SQL script
echo Importing SQL script...
C:\xampp\mysql\bin\mysql -u root gymdb < setup-database.sql

echo Database setup complete!
pause
