import java.time.LocalDate;

public class Registration {
    private int id;
    private int membreId;
    private String membreNom;
    private int abonnementId;
    private String abonnementType;
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private String etat;

    public Registration(int id, int membreId, String membreNom, int abonnementId, String abonnementType, LocalDate dateDebut, LocalDate dateFin, String etat) {
        this.id = id;
        this.membreId = membreId;
        this.membreNom = membreNom;
        this.abonnementId = abonnementId;
        this.abonnementType = abonnementType;
        this.dateDebut = dateDebut;
        this.dateFin = dateFin;
        this.etat = etat;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getMembreId() {
        return membreId;
    }

    public void setMembreId(int membreId) {
        this.membreId = membreId;
    }

    public String getMembreNom() {
        return membreNom;
    }

    public void setMembreNom(String membreNom) {
        this.membreNom = membreNom;
    }

    public int getAbonnementId() {
        return abonnementId;
    }

    public void setAbonnementId(int abonnementId) {
        this.abonnementId = abonnementId;
    }

    public String getAbonnementType() {
        return abonnementType;
    }

    public void setAbonnementType(String abonnementType) {
        this.abonnementType = abonnementType;
    }

    public LocalDate getDateDebut() {
        return dateDebut;
    }

    public void setDateDebut(LocalDate dateDebut) {
        this.dateDebut = dateDebut;
    }

    public LocalDate getDateFin() {
        return dateFin;
    }

    public void setDateFin(LocalDate dateFin) {
        this.dateFin = dateFin;
    }

    public String getEtat() {
        return etat;
    }

    public void setEtat(String etat) {
        this.etat = etat;
    }
}
