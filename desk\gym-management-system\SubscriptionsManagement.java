import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.sql.*;

public class SubscriptionsManagement {

    private Stage primaryStage;
    private MainDashboard dashboard;
    private TableView<Subscription> subscriptionTable;
    private TextField idField, typeField, dureeJoursField, prixField;
    private Label statusLabel;

    public SubscriptionsManagement(Stage primaryStage, MainDashboard dashboard) {
        this.primaryStage = primaryStage;
        this.dashboard = dashboard;
        primaryStage.setTitle("Gestion de la Salle de Sport - Gestion des Abonnements");
    }

    public void show() {
        // Create the subscriptions management layout
        BorderPane layout = createLayout();
        
        // Create the scene
        Scene scene = new Scene(layout, 800, 600);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private BorderPane createLayout() {
        // Create title
        Label titleLabel = new Label("Gestion des Abonnements");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        // Create form for adding/editing subscriptions
        GridPane form = createForm();
        
        // Create table for displaying subscriptions
        subscriptionTable = createTable();
        
        // Create buttons
        HBox buttonBox = createButtonBox();
        
        // Create status label
        statusLabel = new Label("");
        statusLabel.setStyle("-fx-text-fill: red;");
        
        // Create back button
        Button backButton = new Button("Retour au tableau de bord");
        backButton.setOnAction(e -> returnToDashboard());
        
        // Create layout
        VBox topSection = new VBox(10);
        topSection.setAlignment(Pos.CENTER);
        topSection.getChildren().addAll(titleLabel, form, buttonBox, statusLabel);
        
        VBox bottomSection = new VBox(10);
        bottomSection.setAlignment(Pos.CENTER);
        bottomSection.getChildren().addAll(backButton);
        
        BorderPane layout = new BorderPane();
        layout.setPadding(new Insets(20));
        layout.setTop(topSection);
        layout.setCenter(subscriptionTable);
        layout.setBottom(bottomSection);
        
        // Load subscriptions from database
        loadSubscriptions();
        
        return layout;
    }
    
    private GridPane createForm() {
        // Create form elements
        Label idLabel = new Label("ID:");
        idField = new TextField();
        idField.setEditable(false);
        idField.setPromptText("Auto-généré");
        
        Label typeLabel = new Label("Type d'abonnement:");
        typeField = new TextField();
        typeField.setPromptText("Entrez le type d'abonnement");
        
        Label dureeJoursLabel = new Label("Durée (jours):");
        dureeJoursField = new TextField();
        dureeJoursField.setPromptText("Entrez la durée en jours");
        
        Label prixLabel = new Label("Prix:");
        prixField = new TextField();
        prixField.setPromptText("Entrez le prix");
        
        // Create form layout
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(10);
        form.setPadding(new Insets(10));
        
        // Add form elements to layout
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(typeLabel, 0, 1);
        form.add(typeField, 1, 1);
        form.add(dureeJoursLabel, 2, 1);
        form.add(dureeJoursField, 3, 1);
        form.add(prixLabel, 0, 2);
        form.add(prixField, 1, 2);
        
        return form;
    }
    
    private TableView<Subscription> createTable() {
        // Create table columns
        TableColumn<Subscription, Integer> idColumn = new TableColumn<>("ID");
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        TableColumn<Subscription, String> typeColumn = new TableColumn<>("Type d'abonnement");
        typeColumn.setCellValueFactory(new PropertyValueFactory<>("type"));
        
        TableColumn<Subscription, Integer> dureeJoursColumn = new TableColumn<>("Durée (jours)");
        dureeJoursColumn.setCellValueFactory(new PropertyValueFactory<>("dureeJours"));
        
        TableColumn<Subscription, Double> prixColumn = new TableColumn<>("Prix");
        prixColumn.setCellValueFactory(new PropertyValueFactory<>("prix"));
        
        // Create table
        TableView<Subscription> table = new TableView<>();
        table.getColumns().addAll(idColumn, typeColumn, dureeJoursColumn, prixColumn);
        
        // Set column widths
        idColumn.setPrefWidth(50);
        typeColumn.setPrefWidth(200);
        dureeJoursColumn.setPrefWidth(100);
        prixColumn.setPrefWidth(100);
        
        // Add selection listener
        table.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                // Fill form with selected subscription data
                idField.setText(String.valueOf(newSelection.getId()));
                typeField.setText(newSelection.getType());
                dureeJoursField.setText(String.valueOf(newSelection.getDureeJours()));
                prixField.setText(String.valueOf(newSelection.getPrix()));
            }
        });
        
        return table;
    }
    
    private HBox createButtonBox() {
        // Create buttons
        Button addButton = new Button("Ajouter");
        addButton.setOnAction(e -> addSubscription());
        
        Button updateButton = new Button("Modifier");
        updateButton.setOnAction(e -> updateSubscription());
        
        Button deleteButton = new Button("Supprimer");
        deleteButton.setOnAction(e -> deleteSubscription());
        
        Button clearButton = new Button("Effacer");
        clearButton.setOnAction(e -> clearForm());
        
        // Create button layout
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(addButton, updateButton, deleteButton, clearButton);
        
        return buttonBox;
    }
    
    private void loadSubscriptions() {
        ObservableList<Subscription> subscriptions = FXCollections.observableArrayList();
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT * FROM abonnements";
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            
            while (resultSet.next()) {
                Subscription subscription = new Subscription(
                    resultSet.getInt("id"),
                    resultSet.getString("type"),
                    resultSet.getInt("duree_jours"),
                    resultSet.getDouble("prix")
                );
                subscriptions.add(subscription);
            }
            
            subscriptionTable.setItems(subscriptions);
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors du chargement des abonnements: " + e.getMessage());
        }
    }
    
    private void addSubscription() {
        // Validate input
        if (!validateInput()) {
            return;
        }
        
        String type = typeField.getText();
        int dureeJours = Integer.parseInt(dureeJoursField.getText());
        double prix = Double.parseDouble(prixField.getText());
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "INSERT INTO abonnements (type, duree_jours, prix) VALUES (?, ?, ?)";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, type);
            statement.setInt(2, dureeJours);
            statement.setDouble(3, prix);
            
            int rowsInserted = statement.executeUpdate();
            if (rowsInserted > 0) {
                statusLabel.setText("Abonnement ajouté avec succès");
                clearForm();
                loadSubscriptions();
            } else {
                statusLabel.setText("Erreur lors de l'ajout de l'abonnement");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de l'ajout de l'abonnement: " + e.getMessage());
        }
    }
    
    private void updateSubscription() {
        // Check if a subscription is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner un abonnement à modifier");
            return;
        }
        
        // Validate input
        if (!validateInput()) {
            return;
        }
        
        int id = Integer.parseInt(idField.getText());
        String type = typeField.getText();
        int dureeJours = Integer.parseInt(dureeJoursField.getText());
        double prix = Double.parseDouble(prixField.getText());
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "UPDATE abonnements SET type = ?, duree_jours = ?, prix = ? WHERE id = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, type);
            statement.setInt(2, dureeJours);
            statement.setDouble(3, prix);
            statement.setInt(4, id);
            
            int rowsUpdated = statement.executeUpdate();
            if (rowsUpdated > 0) {
                statusLabel.setText("Abonnement modifié avec succès");
                clearForm();
                loadSubscriptions();
            } else {
                statusLabel.setText("Erreur lors de la modification de l'abonnement");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de la modification de l'abonnement: " + e.getMessage());
        }
    }
    
    private void deleteSubscription() {
        // Check if a subscription is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner un abonnement à supprimer");
            return;
        }
        
        int id = Integer.parseInt(idField.getText());
        
        // Confirm deletion
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText(null);
        alert.setContentText("Êtes-vous sûr de vouloir supprimer cet abonnement ?");
        
        if (alert.showAndWait().get() == ButtonType.OK) {
            String dbUrl = "*********************************";
            String dbUser = "root";
            String dbPassword = "";
            
            try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
                // First check if subscription has registrations
                String checkQuery = "SELECT COUNT(*) FROM inscriptions WHERE abonnement_id = ?";
                PreparedStatement checkStatement = connection.prepareStatement(checkQuery);
                checkStatement.setInt(1, id);
                ResultSet resultSet = checkStatement.executeQuery();
                resultSet.next();
                int count = resultSet.getInt(1);
                
                if (count > 0) {
                    statusLabel.setText("Impossible de supprimer cet abonnement car il a des inscriptions");
                    return;
                }
                
                // Delete the subscription
                String query = "DELETE FROM abonnements WHERE id = ?";
                PreparedStatement statement = connection.prepareStatement(query);
                statement.setInt(1, id);
                
                int rowsDeleted = statement.executeUpdate();
                if (rowsDeleted > 0) {
                    statusLabel.setText("Abonnement supprimé avec succès");
                    clearForm();
                    loadSubscriptions();
                } else {
                    statusLabel.setText("Erreur lors de la suppression de l'abonnement");
                }
            } catch (SQLException e) {
                e.printStackTrace();
                statusLabel.setText("Erreur lors de la suppression de l'abonnement: " + e.getMessage());
            }
        }
    }
    
    private boolean validateInput() {
        if (typeField.getText().isEmpty()) {
            statusLabel.setText("Le type d'abonnement est obligatoire");
            return false;
        }
        
        try {
            int dureeJours = Integer.parseInt(dureeJoursField.getText());
            if (dureeJours <= 0) {
                statusLabel.setText("La durée doit être un nombre positif");
                return false;
            }
        } catch (NumberFormatException e) {
            statusLabel.setText("La durée doit être un nombre entier");
            return false;
        }
        
        try {
            double prix = Double.parseDouble(prixField.getText());
            if (prix <= 0) {
                statusLabel.setText("Le prix doit être un nombre positif");
                return false;
            }
        } catch (NumberFormatException e) {
            statusLabel.setText("Le prix doit être un nombre");
            return false;
        }
        
        return true;
    }
    
    private void clearForm() {
        idField.clear();
        typeField.clear();
        dureeJoursField.clear();
        prixField.clear();
        subscriptionTable.getSelectionModel().clearSelection();
    }
    
    private void returnToDashboard() {
        dashboard.show();
    }
}
