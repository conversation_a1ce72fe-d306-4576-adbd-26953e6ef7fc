-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(50) NOT NULL
);

-- Create membres table
CREATE TABLE IF NOT EXISTS membres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    telephone VARCHAR(20),
    date_naissance DATE,
    date_inscription DATE NOT NULL
);

-- Create abonnements table
CREATE TABLE IF NOT EXISTS abonnements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    duree_jours INT NOT NULL,
    prix DECIMAL(10, 2) NOT NULL
);

-- Create inscriptions table
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    membre_id INT NOT NULL,
    abonnement_id INT NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    etat VARCHAR(20) NOT NULL,
    FOREIGN KEY (membre_id) REFERENCES membres(id),
    FOREIGN KEY (abonnement_id) REFERENCES abonnements(id)
);

-- Insert sample data
INSERT INTO users (name, email, password) VALUES ('Admin User', '<EMAIL>', 'admin');

INSERT INTO membres (nom, prenom, email, telephone, date_naissance, date_inscription)
VALUES
    ('Dupont', 'Jean', '<EMAIL>', '0612345678', '1985-05-15', CURDATE()),
    ('Martin', 'Sophie', '<EMAIL>', '0687654321', '1990-10-20', CURDATE()),
    ('Dubois', 'Pierre', '<EMAIL>', '0654321987', '1978-03-25', CURDATE());

INSERT INTO abonnements (type, duree_jours, prix)
VALUES
    ('Mensuel', 30, 50.00),
    ('Trimestriel', 90, 120.00),
    ('Annuel', 365, 400.00);

INSERT INTO inscriptions (membre_id, abonnement_id, date_debut, date_fin, etat)
VALUES
    (1, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'Actif'),
    (2, 2, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 90 DAY), 'Actif'),
    (3, 3, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 365 DAY), 'Actif');
