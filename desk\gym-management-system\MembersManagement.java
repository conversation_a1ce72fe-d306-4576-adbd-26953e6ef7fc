import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.sql.*;
import java.time.LocalDate;

public class MembersManagement {

    private Stage primaryStage;
    private MainDashboard dashboard;
    private TableView<Member> memberTable;
    private TextField idField, nomField, prenomField, emailField, telephoneField;
    private DatePicker dateNaissancePicker;
    private Label statusLabel;

    public MembersManagement(Stage primaryStage, MainDashboard dashboard) {
        this.primaryStage = primaryStage;
        this.dashboard = dashboard;
        primaryStage.setTitle("Gestion de la Salle de Sport - Gestion des Membres");
    }

    public void show() {
        // Create the members management layout
        BorderPane layout = createLayout();
        
        // Create the scene
        Scene scene = new Scene(layout, 800, 600);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private BorderPane createLayout() {
        // Create title
        Label titleLabel = new Label("Gestion des Membres");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        // Create form for adding/editing members
        GridPane form = createForm();
        
        // Create table for displaying members
        memberTable = createTable();
        
        // Create buttons
        HBox buttonBox = createButtonBox();
        
        // Create status label
        statusLabel = new Label("");
        statusLabel.setStyle("-fx-text-fill: red;");
        
        // Create back button
        Button backButton = new Button("Retour au tableau de bord");
        backButton.setOnAction(e -> returnToDashboard());
        
        // Create layout
        VBox topSection = new VBox(10);
        topSection.setAlignment(Pos.CENTER);
        topSection.getChildren().addAll(titleLabel, form, buttonBox, statusLabel);
        
        VBox bottomSection = new VBox(10);
        bottomSection.setAlignment(Pos.CENTER);
        bottomSection.getChildren().addAll(backButton);
        
        BorderPane layout = new BorderPane();
        layout.setPadding(new Insets(20));
        layout.setTop(topSection);
        layout.setCenter(memberTable);
        layout.setBottom(bottomSection);
        
        // Load members from database
        loadMembers();
        
        return layout;
    }
    
    private GridPane createForm() {
        // Create form elements
        Label idLabel = new Label("ID:");
        idField = new TextField();
        idField.setEditable(false);
        idField.setPromptText("Auto-généré");
        
        Label nomLabel = new Label("Nom:");
        nomField = new TextField();
        nomField.setPromptText("Entrez le nom");
        
        Label prenomLabel = new Label("Prénom:");
        prenomField = new TextField();
        prenomField.setPromptText("Entrez le prénom");
        
        Label emailLabel = new Label("Email:");
        emailField = new TextField();
        emailField.setPromptText("Entrez l'email");
        
        Label telephoneLabel = new Label("Téléphone:");
        telephoneField = new TextField();
        telephoneField.setPromptText("Entrez le numéro de téléphone");
        
        Label dateNaissanceLabel = new Label("Date de naissance:");
        dateNaissancePicker = new DatePicker();
        
        // Create form layout
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(10);
        form.setPadding(new Insets(10));
        
        // Add form elements to layout
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(nomLabel, 0, 1);
        form.add(nomField, 1, 1);
        form.add(prenomLabel, 2, 1);
        form.add(prenomField, 3, 1);
        form.add(emailLabel, 0, 2);
        form.add(emailField, 1, 2);
        form.add(telephoneLabel, 2, 2);
        form.add(telephoneField, 3, 2);
        form.add(dateNaissanceLabel, 0, 3);
        form.add(dateNaissancePicker, 1, 3);
        
        return form;
    }
    
    private TableView<Member> createTable() {
        // Create table columns
        TableColumn<Member, Integer> idColumn = new TableColumn<>("ID");
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        TableColumn<Member, String> nomColumn = new TableColumn<>("Nom");
        nomColumn.setCellValueFactory(new PropertyValueFactory<>("nom"));
        
        TableColumn<Member, String> prenomColumn = new TableColumn<>("Prénom");
        prenomColumn.setCellValueFactory(new PropertyValueFactory<>("prenom"));
        
        TableColumn<Member, String> emailColumn = new TableColumn<>("Email");
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        
        TableColumn<Member, String> telephoneColumn = new TableColumn<>("Téléphone");
        telephoneColumn.setCellValueFactory(new PropertyValueFactory<>("telephone"));
        
        TableColumn<Member, LocalDate> dateNaissanceColumn = new TableColumn<>("Date de naissance");
        dateNaissanceColumn.setCellValueFactory(new PropertyValueFactory<>("dateNaissance"));
        
        TableColumn<Member, LocalDate> dateInscriptionColumn = new TableColumn<>("Date d'inscription");
        dateInscriptionColumn.setCellValueFactory(new PropertyValueFactory<>("dateInscription"));
        
        // Create table
        TableView<Member> table = new TableView<>();
        table.getColumns().addAll(idColumn, nomColumn, prenomColumn, emailColumn, telephoneColumn, dateNaissanceColumn, dateInscriptionColumn);
        
        // Set column widths
        idColumn.setPrefWidth(50);
        nomColumn.setPrefWidth(100);
        prenomColumn.setPrefWidth(100);
        emailColumn.setPrefWidth(150);
        telephoneColumn.setPrefWidth(100);
        dateNaissanceColumn.setPrefWidth(120);
        dateInscriptionColumn.setPrefWidth(120);
        
        // Add selection listener
        table.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                // Fill form with selected member data
                idField.setText(String.valueOf(newSelection.getId()));
                nomField.setText(newSelection.getNom());
                prenomField.setText(newSelection.getPrenom());
                emailField.setText(newSelection.getEmail());
                telephoneField.setText(newSelection.getTelephone());
                dateNaissancePicker.setValue(newSelection.getDateNaissance());
            }
        });
        
        return table;
    }
    
    private HBox createButtonBox() {
        // Create buttons
        Button addButton = new Button("Ajouter");
        addButton.setOnAction(e -> addMember());
        
        Button updateButton = new Button("Modifier");
        updateButton.setOnAction(e -> updateMember());
        
        Button deleteButton = new Button("Supprimer");
        deleteButton.setOnAction(e -> deleteMember());
        
        Button clearButton = new Button("Effacer");
        clearButton.setOnAction(e -> clearForm());
        
        // Create button layout
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(addButton, updateButton, deleteButton, clearButton);
        
        return buttonBox;
    }
    
    private void loadMembers() {
        ObservableList<Member> members = FXCollections.observableArrayList();
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT * FROM membres";
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            
            while (resultSet.next()) {
                Member member = new Member(
                    resultSet.getInt("id"),
                    resultSet.getString("nom"),
                    resultSet.getString("prenom"),
                    resultSet.getString("email"),
                    resultSet.getString("telephone"),
                    resultSet.getDate("date_naissance") != null ? resultSet.getDate("date_naissance").toLocalDate() : null,
                    resultSet.getDate("date_inscription") != null ? resultSet.getDate("date_inscription").toLocalDate() : LocalDate.now()
                );
                members.add(member);
            }
            
            memberTable.setItems(members);
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors du chargement des membres: " + e.getMessage());
        }
    }
    
    private void addMember() {
        // Validate input
        if (!validateInput()) {
            return;
        }
        
        String nom = nomField.getText();
        String prenom = prenomField.getText();
        String email = emailField.getText();
        String telephone = telephoneField.getText();
        LocalDate dateNaissance = dateNaissancePicker.getValue();
        LocalDate dateInscription = LocalDate.now();
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "INSERT INTO membres (nom, prenom, email, telephone, date_naissance, date_inscription) VALUES (?, ?, ?, ?, ?, ?)";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, nom);
            statement.setString(2, prenom);
            statement.setString(3, email);
            statement.setString(4, telephone);
            statement.setDate(5, dateNaissance != null ? java.sql.Date.valueOf(dateNaissance) : null);
            statement.setDate(6, java.sql.Date.valueOf(dateInscription));
            
            int rowsInserted = statement.executeUpdate();
            if (rowsInserted > 0) {
                statusLabel.setText("Membre ajouté avec succès");
                clearForm();
                loadMembers();
            } else {
                statusLabel.setText("Erreur lors de l'ajout du membre");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de l'ajout du membre: " + e.getMessage());
        }
    }
    
    private void updateMember() {
        // Check if a member is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner un membre à modifier");
            return;
        }
        
        // Validate input
        if (!validateInput()) {
            return;
        }
        
        int id = Integer.parseInt(idField.getText());
        String nom = nomField.getText();
        String prenom = prenomField.getText();
        String email = emailField.getText();
        String telephone = telephoneField.getText();
        LocalDate dateNaissance = dateNaissancePicker.getValue();
        
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";
        
        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "UPDATE membres SET nom = ?, prenom = ?, email = ?, telephone = ?, date_naissance = ? WHERE id = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, nom);
            statement.setString(2, prenom);
            statement.setString(3, email);
            statement.setString(4, telephone);
            statement.setDate(5, dateNaissance != null ? java.sql.Date.valueOf(dateNaissance) : null);
            statement.setInt(6, id);
            
            int rowsUpdated = statement.executeUpdate();
            if (rowsUpdated > 0) {
                statusLabel.setText("Membre modifié avec succès");
                clearForm();
                loadMembers();
            } else {
                statusLabel.setText("Erreur lors de la modification du membre");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de la modification du membre: " + e.getMessage());
        }
    }
    
    private void deleteMember() {
        // Check if a member is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner un membre à supprimer");
            return;
        }
        
        int id = Integer.parseInt(idField.getText());
        
        // Confirm deletion
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText(null);
        alert.setContentText("Êtes-vous sûr de vouloir supprimer ce membre ?");
        
        if (alert.showAndWait().get() == ButtonType.OK) {
            String dbUrl = "*********************************";
            String dbUser = "root";
            String dbPassword = "";
            
            try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
                // First check if member has registrations
                String checkQuery = "SELECT COUNT(*) FROM inscriptions WHERE membre_id = ?";
                PreparedStatement checkStatement = connection.prepareStatement(checkQuery);
                checkStatement.setInt(1, id);
                ResultSet resultSet = checkStatement.executeQuery();
                resultSet.next();
                int count = resultSet.getInt(1);
                
                if (count > 0) {
                    statusLabel.setText("Impossible de supprimer ce membre car il a des inscriptions");
                    return;
                }
                
                // Delete the member
                String query = "DELETE FROM membres WHERE id = ?";
                PreparedStatement statement = connection.prepareStatement(query);
                statement.setInt(1, id);
                
                int rowsDeleted = statement.executeUpdate();
                if (rowsDeleted > 0) {
                    statusLabel.setText("Membre supprimé avec succès");
                    clearForm();
                    loadMembers();
                } else {
                    statusLabel.setText("Erreur lors de la suppression du membre");
                }
            } catch (SQLException e) {
                e.printStackTrace();
                statusLabel.setText("Erreur lors de la suppression du membre: " + e.getMessage());
            }
        }
    }
    
    private boolean validateInput() {
        if (nomField.getText().isEmpty()) {
            statusLabel.setText("Le nom est obligatoire");
            return false;
        }
        
        if (prenomField.getText().isEmpty()) {
            statusLabel.setText("Le prénom est obligatoire");
            return false;
        }
        
        return true;
    }
    
    private void clearForm() {
        idField.clear();
        nomField.clear();
        prenomField.clear();
        emailField.clear();
        telephoneField.clear();
        dateNaissancePicker.setValue(null);
        memberTable.getSelectionModel().clearSelection();
    }
    
    private void returnToDashboard() {
        dashboard.show();
    }
}
